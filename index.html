<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estúdio730 - Links</title>
    <meta name="description" content="Todos os links do Estúdio730 em um só lugar - WhatsApp, Instagram, Localização e Site Oficial">
    <meta name="theme-color" content="#0a0a0a">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://estudio730.com/">
    <meta property="og:title" content="Estúdio730 - Links">
    <meta property="og:description" content="Estilo e tradição em cada corte. Acesse todos os nossos links em um só lugar.">
    <meta property="og:image" content="logo.webp">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://estudio730.com/">
    <meta property="twitter:title" content="Estúdio730 - Links">
    <meta property="twitter:description" content="Estilo e tradição em cada corte. Acesse todos os nossos links em um só lugar.">
    <meta property="twitter:image" content="logo.webp">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✂️</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- CSS do Choices.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"/>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap"></noscript>

    <!-- Preload logo -->
    <link rel="preload" href="logo.webp" as="image">

    <!-- Inline SVG Icons (replacing Font Awesome) -->
    <style>
        /* Base SVG Icon Styles */
        .icon-svg {
            width: 1.2em;
            height: 1.2em;
            fill: currentColor;
            display: inline-block !important;
            vertical-align: middle;
            flex-shrink: 0;
            transition: all 0.2s ease;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Specific icon colors */
        .icon-whatsapp {
            color: #25d366 !important;
            fill: #25d366 !important;
        }

        .icon-instagram {
            color: #e4405f !important;
            fill: #e4405f !important;
        }

        .icon-location {
            color: #4285f4 !important;
            fill: #4285f4 !important;
        }

        .icon-website {
            color: #6c5ce7 !important;
            fill: #6c5ce7 !important;
        }

        /* Button content layout fixes - Override external CSS */
        .button-content {
            display: flex !important;
            align-items: center !important;
            gap: 16px !important;
        }

        .button-content .icon-svg {
            width: 28px !important;
            height: 28px !important;
            min-width: 28px !important;
            font-size: 28px !important;
        }

        /* Override external CSS for link buttons */
        .link-button .button-content .icon-svg {
            width: 32px !important;
            height: 32px !important;
            min-width: 32px !important;
        }

        /* Arrow icons */
        .arrow {
            width: 16px;
            height: 16px;
            opacity: 0.7;
        }

        /* Footer social icons */
        .social-icons .icon-svg {
            width: 20px;
            height: 20px;
            margin: 0 8px;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .social-icons .icon-svg:hover {
            opacity: 1;
        }

        /* Modal and tab icons */
        .config-header .icon-svg,
        .tab-btn .icon-svg,
        .section-title .icon-svg {
            width: 18px;
            height: 18px;
            margin-right: 8px;
        }

        /* Form button icons */
        .btn-expand-form .icon-svg,
        .btn-add-link .icon-svg,
        .btn-cancel-form .icon-svg,
        .btn-primary .icon-svg,
        .btn-secondary .icon-svg {
            width: 16px;
            height: 16px;
            margin-right: 6px;
        }

        /* Close button icons */
        .config-close .icon-svg,
        .edit-close .icon-svg {
            width: 20px;
            height: 20px;
        }

        /* Skip Link para Acessibilidade */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
            font-size: 14px;
            transition: top 0.3s;
        }
        .skip-link:focus {
            top: 6px;
        }

        /* Melhorar contraste para acessibilidade */
        .footer p {
            color: #e0e0e0;
        }

        /* Ensure SVGs work in all browsers */
        svg {
            pointer-events: none;
        }

        /* Fix for Safari SVG rendering */
        .icon-svg path {
            vector-effect: non-scaling-stroke;
        }



        /* Responsive adjustments for mobile */
        @media (max-width: 768px) {
            .button-content .icon-svg {
                width: 24px !important;
                height: 24px !important;
                min-width: 24px !important;
            }

            .link-button .button-content .icon-svg {
                width: 28px !important;
                height: 28px !important;
                min-width: 28px !important;
            }
        }

        /* Desktop adjustments */
        @media (min-width: 1024px) {
            .button-content .icon-svg {
                width: 32px !important;
                height: 32px !important;
                min-width: 32px !important;
            }

            .link-button .button-content .icon-svg {
                width: 36px !important;
                height: 36px !important;
                min-width: 36px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Skip Link para Acessibilidade -->
    <a href="#main-content" class="skip-link">Pular para o conteúdo principal</a>

    <!-- Botão de Configurações -->
    <button class="config-button" id="config-button"
            title="Configurações"
            aria-label="Abrir configurações"
            aria-expanded="false"
            aria-controls="config-modal">
        <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
    </button>

    <!-- Container principal -->
    <div class="container">
        <!-- Header com logo e nome da barbearia -->
        <header class="header">
            <div class="logo">
                <img src="logo.webp" alt="Estúdio730 Logo" class="logo-image" loading="lazy" decoding="async">
            </div>
            <h1 class="title" id="main-title">Estúdio730</h1>
            <p class="subtitle" id="main-subtitle">Estilo e tradição em cada corte</p>
        </header>

        <!-- Seção de links principais -->
        <main class="links-section" id="main-content">
            <!-- Botão WhatsApp -->
            <a href="#" class="link-button whatsapp" id="whatsapp-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <svg class="icon-svg icon-whatsapp" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                    </svg>
                    <span class="button-text">
                        <strong>WhatsApp</strong>
                        <small>Agende seu horário</small>
                    </span>
                </div>
                <svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
            </a>

            <!-- Botão Instagram -->
            <a href="#" class="link-button instagram" id="instagram-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <svg class="icon-svg icon-instagram" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                    <span class="button-text">
                        <strong>Instagram</strong>
                        <small>Veja nossos trabalhos</small>
                    </span>
                </div>
                <svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
            </a>

            <!-- Botão Localização -->
            <a href="#" class="link-button location" id="location-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <svg class="icon-svg icon-location" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <span class="button-text">
                        <strong>Localização</strong>
                        <small>Como chegar</small>
                    </span>
                </div>
                <svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
            </a>

            <!-- Botão Site -->
            <a href="#" class="link-button website" id="website-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <svg class="icon-svg icon-website" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                    </svg>
                    <span class="button-text">
                        <strong>Site Oficial</strong>
                        <small>Conheça nossos serviços</small>
                    </span>
                </div>
                <svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
            </a>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Estúdio730. Todos os direitos reservados.</p>
            <div class="social-icons">
                <svg class="icon-svg" viewBox="0 0 24 24" aria-label="Instagram">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
                <svg class="icon-svg" viewBox="0 0 24 24" aria-label="Facebook">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <svg class="icon-svg" viewBox="0 0 24 24" aria-label="WhatsApp">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                </svg>
            </div>
        </footer>
    </div>

    <!-- Modal de Configurações -->
    <div class="config-modal" id="config-modal">
        <div class="config-modal-content">
            <!-- Indicador de Swipe para Mobile -->
            <div class="swipe-indicator mobile-only">
                <div class="swipe-handle"></div>
            </div>

            <!-- Header do Modal -->
            <div class="config-header">
                <h2>
                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    Configurações
                </h2>
                <button class="config-close" id="config-close">
                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>

            <!-- Conteúdo do Modal com Navegação por Abas -->
            <div class="config-body">
                <!-- Navegação por Abas -->
                <div class="config-tabs">
                    <div class="tab-navigation" id="tab-navigation" role="tablist">
                        <button class="tab-btn active" data-tab="manage" role="tab" aria-controls="manage-panel">
                            <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z"/>
                            </svg>
                            <span>Gerenciar</span>
                        </button>
                        <button class="tab-btn" data-tab="add" role="tab" aria-controls="add-panel">
                            <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                            <span>Adicionar</span>
                        </button>
                        <button class="tab-btn" data-tab="settings" role="tab" aria-controls="settings-panel">
                            <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                            </svg>
                            <span>Config</span>
                        </button>
                    </div>

                    <!-- Conteúdo das Abas -->
                    <div class="tab-content">
                        <!-- Aba Gerenciar Links -->
                        <div class="tab-panel active" id="manage-panel" role="tabpanel">
                            <div class="config-section">
                                <h3 class="section-title desktop-only">
                                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z"/>
                                    </svg>
                                    Gerenciar Links
                                </h3>
                                <div class="links-list" id="links-list">
                                    <!-- Links serão inseridos dinamicamente aqui -->
                                </div>
                            </div>
                        </div>

                        <!-- Aba Adicionar Link -->
                        <div class="tab-panel" id="add-panel" role="tabpanel">
                            <div class="config-section">
                                <h3 class="section-title desktop-only">
                                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    Adicionar Novo Link
                                </h3>

                                <!-- Botão para expandir formulário -->
                                <button class="btn-expand-form" id="btn-expand-form">
                                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    Adicionar Novo Link
                                </button>

                                <!-- Formulário expansível -->
                                <div class="add-link-form-container" id="add-link-form-container">
                        <form class="add-link-form" id="add-link-form">
                            <div class="form-group">
                                <label for="link-name">Nome do Serviço *</label>
                                <input type="text" id="link-name" placeholder="Ex: Facebook, TikTok, Agendamento" required>
                            </div>

                            <div class="form-group">
                                <label for="link-url">URL do Link *</label>
                                <div class="url-input-group">
                                    <select id="link-protocol" class="protocol-selector" required>
                                        <option value="https">https://</option>
                                        <option value="http">http://</option>
                                    </select>
                                    <input type="text" id="link-url" placeholder="www.exemplo.com" required>
                                </div>
                            </div>

                            <div class="form-group">

                                    <label for="link-icon">Ícone do Serviço</label>
                                    <div class="icon-selector">
                                        <select id="link-icon" required>
                                            <!-- Opções serão populadas via JavaScript -->
                                        </select>
                                        <div class="icon-preview" id="icon-preview">
                                            <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                                            </svg>
                                        </div>
                                    </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-cancel-form" id="btn-cancel-form">
                                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                    </svg>
                                    Cancelar
                                </button>
                                <button type="submit" class="btn-add-link">
                                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    Adicionar Link
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Aba Configurações -->
            <div class="tab-panel" id="settings-panel" role="tabpanel">
                <div class="config-section">
                    <h3 class="section-title desktop-only">
                        <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                        Configurações Avançadas
                    </h3>

                    <div class="settings-options">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Nome Inicial</h4>
                                <small>Defina o nome que aparece no topo da página</small>
                            </div>
                            <div class="setting-control">
                                <input type="text" id="initial-name" class="form-input" placeholder="Digite o nome inicial" maxlength="50">
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Subtítulo</h4>
                                <small>Defina a frase que aparece abaixo do nome principal</small>
                            </div>
                            <div class="setting-control">
                                <input type="text" id="subtitle-text" class="form-input" placeholder="Digite o subtítulo" maxlength="100">
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Tema da Interface</h4>
                                <small>Escolha entre tema claro ou escuro</small>
                            </div>
                            <div class="setting-control">
                                <select id="theme-selector" class="form-select">
                                    <option value="dark">Escuro</option>
                                    <option value="light">Claro</option>
                                    <option value="auto">Automático</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Animações</h4>
                                <small>Ativar/desativar efeitos visuais</small>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" id="animations-toggle">
                                </div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Backup Automático</h4>
                                <small>Salvar configurações automaticamente</small>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" id="backup-toggle">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer do Modal -->
            <div class="config-footer">
                <button class="btn-secondary" id="btn-restore">
                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                    </svg>
                    Restaurar Padrões
                </button>
                <div class="footer-actions">
                    <button class="btn-secondary" id="btn-cancel">Cancelar</button>
                    <button class="btn-primary" id="btn-save">
                        <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                        </svg>
                        Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Link -->
    <div class="edit-modal" id="edit-modal">
        <div class="edit-modal-content">
            <div class="edit-header">
                <h3>
                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                    Editar Link
                </h3>
                <button class="edit-close" id="edit-close">
                    <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>

            <div class="edit-body">
                <form class="edit-link-form" id="edit-link-form">
                    <input type="hidden" id="edit-link-id">

                    <div class="form-group">
                        <label for="edit-link-name">Nome do Serviço *</label>
                        <input type="text" id="edit-link-name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit-link-url">URL do Link *</label>
                        <div class="url-input-group">
                            <select id="edit-link-protocol" class="protocol-selector" required>
                                <option value="https">https://</option>
                                <option value="http">http://</option>
                            </select>
                            <input type="text" id="edit-link-url" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-link-icon">Ícone do Serviço</label>
                        <div class="icon-selector">
                            <select id="edit-link-icon" required>
                                <!-- Opções serão populadas via JavaScript -->
                            </select>
                            <div class="icon-preview" id="edit-icon-preview">
                                <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="edit-actions">
                        <button type="button" class="btn-secondary" id="btn-cancel-edit">
                            Cancelar
                        </button>
                        <button type="submit" class="btn-primary">
                            <svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                            </svg>
                            Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    
    <script src="config-system.js"></script>
    <!-- Choices.js -->
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
</body>
</html>
